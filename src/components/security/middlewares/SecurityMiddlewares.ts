import helmet from 'helmet';
import hpp from 'hpp';
import { Request, Response, NextFunction } from 'express';

import { Var } from '../../../global/var';
import { logger } from '../../../global/services';
import { GenerateCsrfToken, DoubleCsrfProtection } from './CsrfUtils';

// SECURITY HARDENING: Enhanced Content Security Policy with stricter directives
const getCSPOptions = () => {
  const baseDirectives: any = {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "'strict-dynamic'"],
    styleSrc: ["'self'", 'https://fonts.googleapis.com'], // HARDENING: Removed unsafe-inline
    imgSrc: ["'self'", 'data:', 'https:', 'blob:'],
    connectSrc: ["'self'", 'https://api.postmarkapp.com'],
    fontSrc: ["'self'", 'https://fonts.gstatic.com'],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    frameSrc: ["'none'"],
    frameAncestors: ["'none'"],
    baseUri: ["'self'"],
    formAction: ["'self'"],
    upgradeInsecureRequests: [], // HARDENING: Force HTTPS (empty array enables directive)
    blockAllMixedContent: [], // HARDENING: Block mixed content (empty array enables directive)
  };

  // In production, we want to keep these security directives enabled
  // In development, we might want to disable them for local HTTP testing
  if (Var.node.env === 'dev') {
    // Remove these directives in development to allow HTTP resources
    delete baseDirectives.upgradeInsecureRequests;
    delete baseDirectives.blockAllMixedContent;
  }

  return {
    directives: baseDirectives,
    reportOnly: false,
  };
};

export const SecurityHeaders = (req: Request, res: Response, next: NextFunction) => {
  helmet({
    contentSecurityPolicy: getCSPOptions(),
    crossOriginEmbedderPolicy: true,
    crossOriginOpenerPolicy: true,
    crossOriginResourcePolicy: { policy: 'same-site' },
    dnsPrefetchControl: { allow: false },
    frameguard: { action: 'deny' },
    hidePoweredBy: true,
    hsts: {
      maxAge: 15552000, // 180 days
      includeSubDomains: true,
      preload: true,
    },
    ieNoOpen: true,
    noSniff: true,
    originAgentCluster: true,
    permittedCrossDomainPolicies: { permittedPolicies: 'none' },
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
    xssFilter: true,
  })(req, res, next);
};

export const PreventParamPollution = hpp();

export const CsrfProtection = (req: Request, res: Response, next: NextFunction) => {
  // Check if response has already been sent to prevent double responses
  if (res.headersSent) {
    logger.warn('CSRF protection called after headers sent:', {
      path: req.path,
      method: req.method,
    });
    return;
  }

  if (
    req.path.startsWith('/v1/public-surveys') ||
    req.path === '/health' ||
    req.path.includes('/auth/session-check') ||
    req.path.includes('/csp-report') ||
    req.path.includes('/waitlist')
  ) {
    logger.debug('Skipping CSRF protection for exempted path:', { path: req.path });
    // DEBUG: Console output in development only
    if (Var.node.env === 'dev') {
      console.log(`[DEV] Skipping CSRF protection for exempted path: ${req.path}`);
    }
    return next();
  }

  // Skip token generation for the dedicated CSRF token endpoint to avoid double generation
  if (req.path.includes('/csrf-token') && (req.method === 'GET' || req.method === 'HEAD' || req.method === 'OPTIONS')) {
    logger.debug('Skipping CSRF token generation for dedicated endpoint:', { path: req.path });
    // DEBUG: Console output in development only
    if (Var.node.env === 'dev') {
      console.log(`[DEV] Skipping CSRF token generation for dedicated endpoint: ${req.path}`);
    }
    return next();
  }

  if (req.method === 'GET' || req.method === 'HEAD' || req.method === 'OPTIONS') {
    try {
      const token = GenerateCsrfToken(req, res);

      res.setHeader('X-CSRF-Token', token);

      logger.debug('Generated CSRF token:', {
        token: token.substring(0, 10) + '...',
        path: req.path,
        sessionId: req.session?.id ? req.session.id.substring(0, 5) + '...' : 'no-session',
      });

      // DEBUG: Console output in development only
      if (Var.node.env === 'dev') {
        console.log(`[DEV] Generated CSRF token for ${req.method} ${req.path}`);
      }

      return next();
    } catch (error) {
      // Check if response has already been sent before sending error response
      if (res.headersSent) {
        logger.warn('CSRF token generation error after headers sent:', {
          path: req.path,
          method: req.method,
          error: error instanceof Error ? error.message : String(error),
        });
        return;
      }

      logger.error('Error generating CSRF token:', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        session: req.session ? 'exists' : 'missing',
        cookies: req.cookies,
        path: req.path,
        method: req.method,
      });

      res.status(500).json({
        success: false,
        message: 'Error generating security token. Please ensure cookies are enabled in your browser.',
        details: Var.node.env === 'dev' ? (error instanceof Error ? error.message : String(error)) : undefined,
      });
      return; // Explicitly return to prevent further execution
    }
  }

  DoubleCsrfProtection(req, res, next);
};

export const SanitizeData = (req: Request, _res: Response, next: NextFunction) => {
  try {
    if (req.body) {
      req.body = sanitizeObject(req.body);
    }

    if (req.query) {
      req.query = sanitizeObject(req.query);
    }

    if (req.params) {
      req.params = sanitizeObject(req.params);
    }

    next();
  } catch (error) {
    logger.error('Error in data sanitization:', error);
    return next();
  }
};

function sanitizeObject(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string') {
    return sanitizeString(obj);
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item));
  }

  if (typeof obj === 'object') {
    const sanitized: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        sanitized[key] = sanitizeObject(obj[key]);
      }
    }
    return sanitized;
  }

  return obj;
}

// SECURITY HARDENING: Enhanced string sanitization with additional protections
function sanitizeString(str: string): string {
  if (typeof str !== 'string') {
    return str;
  }

  // Remove null bytes and other control characters except newlines and tabs
  let sanitized = str.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

  // Enhanced XSS pattern removal
  sanitized = sanitized
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
    .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/vbscript:/gi, '')
    .replace(/data:text\/html/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .replace(/expression\s*\(/gi, '');

  // Enhanced SQL injection pattern removal - FIXED: Don't remove valid inputType values
  const validInputTypes = ['text', 'email', 'select', 'radio', 'checkbox', 'number'];
  if (!validInputTypes.includes(str.toLowerCase())) {
    sanitized = sanitized
      .replace(/(\b(union|select|insert|update|delete|drop|create|alter|exec|execute|declare|cast|convert)\b)/gi, '')
      .replace(/(-{2,}|\/\*|\*\/|;|\||&)/g, '')
      .replace(/(\bor\b|\band\b)\s+\d+\s*=\s*\d+/gi, '');
  }

  // Remove potential LDAP injection patterns
  sanitized = sanitized.replace(/[()&|!]/g, '');

  // Remove potential command injection patterns
  sanitized = sanitized.replace(/[;&|`$(){}[\]]/g, '');

  // Limit string length to prevent DoS
  if (sanitized.length > 5000) {
    // HARDENING: Reduced from 10000 to 5000
    sanitized = sanitized.substring(0, 5000);
    logger.warn('String truncated due to excessive length', {
      originalLength: str.length,
      truncatedLength: sanitized.length,
    });
  }

  return sanitized;
}
